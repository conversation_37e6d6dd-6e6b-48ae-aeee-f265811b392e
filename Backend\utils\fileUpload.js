const multer = require('multer');
const path = require('path');
const fs = require('fs');
const ErrorResponse = require('./errorResponse');
const { getS3Instance, hasAWSCredentials } = require('./storageHelper');

// Get S3 instance from storage helper
const s3 = getS3Instance();

// Ensure directory exists
const ensureDirectoryExists = (dirPath) => {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`Created directory: ${dirPath}`);
  }
};

// File type and size limits
const FILE_LIMITS = {
  Video: {
    maxSize: 500 * 1024 * 1024, // 500MB
    allowedTypes: /mp4|mov|avi|webm/,
    allowedMimes: [
      'video/mp4',
      'video/quicktime',
      'video/x-msvideo',
      'video/webm'
    ]
  },
  Document: {
    maxSize: 50 * 1024 * 1024, // 50MB
    allowedTypes: /pdf/,
    allowedMimes: [
      'application/pdf'
    ]
  },
  Image: {
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: /jpeg|jpg|png|gif/,
    allowedMimes: [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif'
    ]
  }
};

// Check file type with specific validation for different file types
const checkFileType = (file, cb) => {
  // Determine file category based on field name or content type
  let category = 'Document'; // Default to Document

  if (file.fieldname === 'thumbnail' || file.fieldname === 'profileImage' || file.mimetype.startsWith('image/')) {
    category = 'Image';
  } else if (file.mimetype.startsWith('video/')) {
    category = 'Video';
  }

  const limits = FILE_LIMITS[category];

  // Check file size
  if (file.size > limits.maxSize) {
    const maxSizeMB = limits.maxSize / (1024 * 1024);
    return cb(new ErrorResponse(`File size must be less than ${maxSizeMB}MB`, 400));
  }

  // Check file extension
  const extname = limits.allowedTypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = limits.allowedMimes.includes(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    const allowedExts = limits.allowedTypes.toString().replace(/[/|]/g, ', ').toUpperCase();
    return cb(new ErrorResponse(`Only ${allowedExts} formats are allowed`, 400));
  }
};

// Custom storage engine that handles both S3 and local storage
const customStorage = {
  _handleFile: function (req, file, cb) {
    const timestamp = Date.now();
    const sanitizedName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '-');
    const fileName = `${timestamp}-${sanitizedName}`;

    // Force profile images to always use local storage
    if (file.fieldname === 'profileImage') {
      console.log('[FileUpload] Starting local upload for profile image:', file.originalname);
      const folder = './uploads/profiles/';

      // Ensure directory exists
      if (!fs.existsSync(folder)) {
        fs.mkdirSync(folder, { recursive: true });
        console.log('[FileUpload] Created directory:', folder);
      }

      const filePath = path.join(folder, fileName);
      const writeStream = fs.createWriteStream(filePath);

      let uploadedBytes = 0;

      // Track upload progress
      file.stream.on('data', (chunk) => {
        uploadedBytes += chunk.length;
        // Log progress every 10MB for large files
        if (uploadedBytes % (10 * 1024 * 1024) === 0) {
          console.log(`[FileUpload] Local upload progress: ${Math.round(uploadedBytes / (1024 * 1024))}MB uploaded`);
        }
      });

      file.stream.pipe(writeStream);

      writeStream.on('error', (err) => {
        console.error('[FileUpload] Local upload error:', err);
        cb(err);
      });

      writeStream.on('finish', () => {
        console.log('[FileUpload] Profile image upload successful:', filePath);
        console.log(`[FileUpload] Final size: ${Math.round(uploadedBytes / (1024 * 1024))}MB`);
        cb(null, {
          filename: fileName,
          path: filePath,
          size: uploadedBytes
        });
      });
    } else if (hasAWSCredentials() && s3) {
      // S3 upload for non-profile files
      console.log('[FileUpload] Starting S3 upload for:', file.originalname);
      const s3Key = `uploads/${file.fieldname}/${fileName}`;

      const uploadParams = {
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: s3Key,
        Body: file.stream,
        ContentType: file.mimetype,
        // Removed ACL setting - bucket policy handles public access
        Metadata: {
          fieldName: file.fieldname,
          originalName: file.originalname,
          uploadTime: timestamp.toString()
        }
      };

      // Use managed upload for better handling of large files
      const managedUpload = s3.upload(uploadParams);

      // Track upload progress
      managedUpload.on('httpUploadProgress', (progress) => {
        const percent = Math.round((progress.loaded / progress.total) * 100);
        console.log(`[FileUpload] S3 upload progress: ${percent}% (${progress.loaded}/${progress.total} bytes)`);
      });

      managedUpload.send((err, data) => {
        if (err) {
          console.error('[FileUpload] S3 upload error:', err);
          return cb(err);
        }

        console.log('[FileUpload] S3 upload successful:', data.Location);
        cb(null, {
          bucket: data.Bucket,
          key: data.Key,
          location: data.Location,
          etag: data.ETag,
          size: file.size,
          // Store additional info for signed URL generation
          s3Key: data.Key,
          s3Bucket: data.Bucket
        });
      });
    } else {
      // Local upload for non-profile files
      console.log('[FileUpload] Starting local upload for:', file.originalname);
      const folder = './uploads/';

      // Ensure directory exists
      if (!fs.existsSync(folder)) {
        fs.mkdirSync(folder, { recursive: true });
        console.log('[FileUpload] Created directory:', folder);
      }

      const filePath = path.join(folder, fileName);
      const writeStream = fs.createWriteStream(filePath);

      let uploadedBytes = 0;

      // Track upload progress
      file.stream.on('data', (chunk) => {
        uploadedBytes += chunk.length;
        // Log progress every 10MB for large files
        if (uploadedBytes % (10 * 1024 * 1024) === 0) {
          console.log(`[FileUpload] Local upload progress: ${Math.round(uploadedBytes / (1024 * 1024))}MB uploaded`);
        }
      });

      file.stream.pipe(writeStream);

      writeStream.on('error', (err) => {
        console.error('[FileUpload] Local upload error:', err);
        cb(err);
      });

      writeStream.on('finish', () => {
        console.log('[FileUpload] Local upload successful:', filePath);
        console.log(`[FileUpload] Final size: ${Math.round(uploadedBytes / (1024 * 1024))}MB`);
        cb(null, {
          filename: fileName,
          path: filePath,
          size: uploadedBytes
        });
      });
    }
  },

  _removeFile: function (req, file, cb) {
    // Cleanup function if needed
    cb(null);
  }
};

// Use custom storage that handles both S3 and local automatically
const storage = customStorage;
console.log('[FileUpload] Using custom storage engine (auto-detects S3/Local)');

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 500000000, // 500MB for video files
    fieldSize: 50000000,  // 50MB for field data
    files: 10             // Maximum 10 files
  },
  fileFilter: function (req, file, cb) {
    checkFileType(file, cb);
  }
});

// Add error handling wrapper for upload
const uploadWithErrorHandling = {
  single: (fieldName) => {
    return (req, res, next) => {
      const uploadSingle = upload.single(fieldName);
      uploadSingle(req, res, (err) => {
        if (err) {
          console.error('[FileUpload] Upload error:', err);

          // If S3 error and we have S3 configured, try to provide more details
          if (err.message && err.message.includes('client.send')) {
            console.error('[FileUpload] S3 client error detected. This might be due to AWS SDK configuration issues.');
            return next(new ErrorResponse('File upload failed due to storage configuration issue. Please try again or contact support.', 500));
          }

          return next(new ErrorResponse(`File upload failed: ${err.message}`, 400));
        }
        next();
      });
    };
  }
};

// Export both upload middleware and helper functions
module.exports = {
  upload: uploadWithErrorHandling,
  uploadRaw: upload, // Export raw upload for diagnostics
  hasAWSCredentials,
  isUsingS3Storage: () => hasAWSCredentials() && s3 !== null
};
