/**
 * Upload Configuration
 * Centralized configuration for file upload limits and timeouts
 */

// File size limits in bytes
const FILE_SIZE_LIMITS = {
  VIDEO: 1024 * 1024 * 1024, // 1GB for video files
  DOCUMENT: 50 * 1024 * 1024, // 50MB for documents
  IMAGE: 5 * 1024 * 1024, // 5MB for images
  THUMBNAIL: 5 * 1024 * 1024, // 5MB for thumbnails
  PROFILE_IMAGE: 5 * 1024 * 1024, // 5MB for profile images
};

// Timeout configurations in milliseconds
const TIMEOUT_CONFIG = {
  UPLOAD_TIMEOUT: 3600000, // 1 hour (3600 seconds)
  PROFILE_UPLOAD_TIMEOUT: 600000, // 10 minutes for profile images
  SERVER_TIMEOUT: 3600000, // 1 hour for server timeout
  CLIENT_TIMEOUT: 3600000, // 1 hour for client timeout
  PROXY_TIMEOUT: 3600000, // 1 hour for proxy timeout
};

// Supported file types
const SUPPORTED_TYPES = {
  VIDEO: {
    extensions: /mp4|mov|avi|webm/,
    mimeTypes: [
      'video/mp4',
      'video/quicktime',
      'video/x-msvideo',
      'video/webm'
    ]
  },
  DOCUMENT: {
    extensions: /pdf/,
    mimeTypes: [
      'application/pdf'
    ]
  },
  IMAGE: {
    extensions: /jpeg|jpg|png|gif/,
    mimeTypes: [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif'
    ]
  }
};

// Multer configuration
const MULTER_CONFIG = {
  fileSize: FILE_SIZE_LIMITS.VIDEO, // Use largest size as default
  fieldSize: 50 * 1024 * 1024, // 50MB for field data
  files: 10, // Maximum number of files
};

// Express body parser limits
const BODY_PARSER_LIMITS = {
  json: '1gb',
  urlencoded: '1gb'
};

// Helper functions
const getFileSizeLimit = (fileType) => {
  const type = fileType.toUpperCase();
  return FILE_SIZE_LIMITS[type] || FILE_SIZE_LIMITS.VIDEO;
};

const getTimeoutForFileType = (fileType) => {
  if (fileType === 'profile') {
    return TIMEOUT_CONFIG.PROFILE_UPLOAD_TIMEOUT;
  }
  return TIMEOUT_CONFIG.UPLOAD_TIMEOUT;
};

const formatFileSize = (bytes) => {
  if (bytes >= 1024 * 1024 * 1024) {
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)}GB`;
  } else if (bytes >= 1024 * 1024) {
    return `${(bytes / (1024 * 1024)).toFixed(0)}MB`;
  } else if (bytes >= 1024) {
    return `${(bytes / 1024).toFixed(0)}KB`;
  }
  return `${bytes}B`;
};

const isValidFileType = (file, allowedType) => {
  const typeConfig = SUPPORTED_TYPES[allowedType.toUpperCase()];
  if (!typeConfig) return false;

  const extname = typeConfig.extensions.test(file.originalname.toLowerCase());
  const mimetype = typeConfig.mimeTypes.includes(file.mimetype);

  return mimetype && extname;
};

module.exports = {
  FILE_SIZE_LIMITS,
  TIMEOUT_CONFIG,
  SUPPORTED_TYPES,
  MULTER_CONFIG,
  BODY_PARSER_LIMITS,
  getFileSizeLimit,
  getTimeoutForFileType,
  formatFileSize,
  isValidFileType
};
